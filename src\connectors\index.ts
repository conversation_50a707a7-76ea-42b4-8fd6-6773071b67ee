/**
 * Dynamic Connector Selection System
 * 
 * This module provides automatic routing between Airtable and NocoDB connectors
 * based on the DATA_CONNECTOR environment variable.
 * 
 * Usage:
 * - Set DATA_CONNECTOR=airtable to use Airtable connector
 * - Set DATA_CONNECTOR=nocodb to use NocoDB connector
 * - Defaults to nocodb if not specified
 * 
 * All imports throughout the application should use this module instead of
 * importing connectors directly to ensure seamless switching.
 */

const DATA_CONNECTOR = import.meta.env.DATA_CONNECTOR || 'nocodb';

console.log(`Using ${DATA_CONNECTOR.toUpperCase()} connector for data operations`);

let connectorModule: any;

try {
    if (DATA_CONNECTOR.toLowerCase() === 'airtable') {
        connectorModule = await import('./airtable.js');
        console.log('Airtable connector loaded successfully');
    } else if (DATA_CONNECTOR.toLowerCase() === 'nocodb') {
        connectorModule = await import('./nocodb.js');
        console.log('NocoDB connector loaded successfully');
    } else {
        console.warn(`Unknown DATA_CONNECTOR value: ${DATA_CONNECTOR}. Falling back to NocoDB.`);
        connectorModule = await import('./nocodb.js');
    }
} catch (error) {
    console.error(`Failed to load ${DATA_CONNECTOR} connector:`, error);
    console.log('Attempting to load fallback connector...');
    
    try {
        // Try to load the other connector as fallback
        if (DATA_CONNECTOR.toLowerCase() === 'airtable') {
            console.log('Falling back to NocoDB connector');
            connectorModule = await import('./nocodb.js');
        } else {
            console.log('Falling back to Airtable connector');
            connectorModule = await import('./airtable.js');
        }
    } catch (fallbackError) {
        console.error('Failed to load fallback connector:', fallbackError);
        throw new Error('No working connector could be loaded');
    }
}

// Re-export all connector functions and data with the same interface
export const {
    latinEvents,
    socialEvents,
    reviews,
    vocabulary,
    bio,
    settings,
    properties,
    services,
    fetchTablesFromAirdata,
    fetchTablesFromNocoDB
} = connectorModule;

// Export connector-specific functions if they exist
export const fetchTables = connectorModule.fetchTablesFromAirdata || connectorModule.fetchTablesFromNocoDB;

// Export metadata about the active connector
export const activeConnector = DATA_CONNECTOR.toLowerCase();
export const isAirtable = activeConnector === 'airtable';
export const isNocoDB = activeConnector === 'nocodb';

console.log(`Dynamic connector system initialized with ${activeConnector} connector`);
