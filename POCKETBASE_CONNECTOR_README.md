# PocketBase Connector for AstroLiveGC

This document provides information about the newly implemented PocketBase connector that provides 100% drop-in replacement compatibility with the existing Airtable and NocoDB connectors.

## Overview

The PocketBase connector allows you to use PocketBase as your data source while maintaining complete compatibility with existing components. It follows the same patterns and interfaces as the Airtable and NocoDB connectors.

## Features

### ✅ Complete Implementation
- **Admin Authentication**: Uses superuser credentials for full data access
- **Schema-based Field Detection**: Automatically detects field types from PocketBase collection schemas
- **Dynamic Field Transformation**: Handles file attachments, multi-select fields, and relationships
- **Comprehensive Pagination**: Fetches ALL records from collections using proper pagination
- **File URL Generation**: Converts PocketBase file references to full URLs
- **100% Drop-in Compatibility**: Same interface as Airtable and NocoDB connectors
- **Error Handling**: Graceful fallbacks and comprehensive error logging

### ✅ Data Transformation
- **File Attachments**: Converts PocketBase file references to Airtable-compatible format with full URLs
- **Multi-select Fields**: Transforms comma-separated values to arrays
- **Date Fields**: Normalizes dates to ISO format
- **Relation Fields**: Converts PocketBase relations to Airtable format with `rec` prefix
- **Boolean Fields**: Handles string and boolean conversions
- **Dynamic Detection**: Automatically detects field types when schema is unavailable

### ✅ Integration
- **Dynamic Connector System**: Integrated with existing multi-connector switching
- **Environment Variables**: Proper configuration support
- **TypeScript Definitions**: Full type support
- **Documentation**: Updated usage documentation

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env` file:

```bash
# PocketBase Configuration
POCKETBASE_URL=https://your-pocketbase-instance.com/
POCKETBASE_ADMIN_EMAIL=<EMAIL>
POCKETBASE_ADMIN_PASSWORD=your-admin-password

# Set the connector to use PocketBase
DATA_CONNECTOR=pocketbase
```

### 2. Collection Configuration

The connector uses the same `TABLE_NAMES` configuration as other connectors:

- social_agenda
- dancing_agenda
- reviews
- vocabulary
- bio
- services
- housing
- settings

Make sure your PocketBase collections match these names.

## Usage

### Basic Usage

```javascript
// Import from the dynamic connector system
import {
    latinEvents,
    socialEvents,
    reviews,
    vocabulary,
    bio,
    settings,
    properties,
    services
} from '~/connectors/index';

// Use the data exactly as you would with Airtable or NocoDB
console.log('Latin Events:', latinEvents);
console.log('Social Events:', socialEvents);
```

### API Functions

```javascript
import { fetchTablesFromPocketBase } from '~/connectors/index';

// Fetch all tables programmatically
const tables = await fetchTablesFromPocketBase();
```

### Switching Between Connectors

Simply change the `DATA_CONNECTOR` environment variable:

```bash
# Use PocketBase
DATA_CONNECTOR=pocketbase

# Use NocoDB
DATA_CONNECTOR=nocodb

# Use Airtable
DATA_CONNECTOR=airtable
```

**No code changes required!** The dynamic connector system automatically loads the appropriate connector.

## Field Type Support

The PocketBase connector supports all common PocketBase field types:

- **Text Fields**: `text`, `email`, `url`, `editor`
- **Numeric Fields**: `number`
- **Boolean Fields**: `bool`
- **Date Fields**: `date`
- **File Fields**: `file` (with automatic URL generation)
- **Select Fields**: `select` (both single and multi-select)
- **Relation Fields**: `relation`
- **JSON Fields**: `json`

## File Handling

The connector automatically handles PocketBase file fields:

1. **URL Generation**: Converts PocketBase file references to full URLs using `pb.files.getUrl()`
2. **MIME Type Detection**: Determines MIME types based on file extensions
3. **Thumbnail Support**: Provides Airtable-compatible thumbnail structure
4. **Multiple Files**: Handles both single and multiple file attachments

## Error Handling

The connector includes comprehensive error handling:

- **Authentication Failures**: Graceful handling of admin authentication issues
- **Network Errors**: Retry logic and fallbacks for network issues
- **Schema Errors**: Falls back to dynamic detection when schema fetching fails
- **Data Transformation Errors**: Preserves original values when transformation fails

## Compatibility

The PocketBase connector is designed to be a 100% drop-in replacement:

- ✅ Same export interface as Airtable/NocoDB connectors
- ✅ Same data structure format
- ✅ Same `get()` method support on records
- ✅ Same field transformation patterns
- ✅ Same error handling approach

## Limitations

As per requirements, the following features are **intentionally not implemented**:

- ❌ OG image creation logic (excluded as requested)
- ❌ OG image update functionality (excluded as requested)

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify `POCKETBASE_ADMIN_EMAIL` and `POCKETBASE_ADMIN_PASSWORD` are correct
   - Ensure the admin user has proper permissions

2. **Collection Not Found**
   - Verify collection names match the `TABLE_NAMES` configuration
   - Check that collections exist in your PocketBase instance

3. **File URL Issues**
   - Ensure `POCKETBASE_URL` includes the protocol (http:// or https://)
   - Verify file fields contain valid file references

### Debug Mode

Enable debug logging by checking the console output. The connector provides detailed logging for:

- Authentication status
- Collection metadata fetching
- Record transformation
- Error conditions

## Migration from Other Connectors

Migrating from Airtable or NocoDB to PocketBase is seamless:

1. Set up your PocketBase instance with matching collection names
2. Update your environment variables
3. Change `DATA_CONNECTOR=pocketbase`
4. Restart your application

No code changes are required in your components or pages.
