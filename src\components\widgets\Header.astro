---
import { Icon } from 'astro-icon/components';
import Logo from '~/components/Logo.astro';
import ToggleTheme from '~/components/common/ToggleTheme.astro';
import ToggleMenu from '~/components/common/ToggleMenu.astro';
import LanguagePicker from '~/components/common/LanguagePicker.astro';

import { trimSlash, getAsset } from '~/utils/permalinks';

import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';
import { defaultLang } from '~/i18n/ui';

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

const processRoute = (route, language) => {
  const computedRoute = `${route ? route : ''}`;
  const pathTranslated = translatePath(computedRoute, language);
  if (pathTranslated === '/' || pathTranslated === '//') return '/';
  return pathTranslated.replace(///{1,2}$/, ''); // Remove one/two slashes at the end
};

interface Link {
  text?: string;
  href?: string;
  ariaLabel?: string;
  icon?: string;
}

interface ActionLink extends Link {
  class?: string;
  type?: string;
}

interface MenuLink extends Link {
  links?: Array<MenuLink>;
}

export interface Props {
  id?: string;
  links?: Array<MenuLink>;
  actions?: Array<ActionLink>;
  isSticky?: boolean;
  isDark?: boolean;
  isFullWidth?: boolean;
  showToggleTheme?: boolean;
  showRssFeed?: boolean;
  position?: string;
}

const {
  id = 'header',
  links = [],
  actions = [],
  isSticky = false,
  isDark = false,
  isFullWidth = false,
  showToggleTheme = false,
  showRssFeed = false,
  position = 'center',
} = Astro.props;

const currentPath = `/${trimSlash(new URL(Astro.url).pathname)}`;
---

<header
  class:list={[
    { sticky: isSticky, relative: !isSticky, dark: isDark },
    'top-0 z-40 flex-none mx-auto w-full border-b border-gray-50/0 transition-[opacity] ease-in-out',
  ]}
  {...id ? { id } : {}}
>
  <div class="absolute inset-0"></div>
  <div
    class:list={[
      'relative text-default py-3 px-3 md:px-6 mx-auto w-full md:flex md:justify-between',
      {
        'max-w-7xl': !isFullWidth,
      },
    ]}
  >
    <div class:list={[{ 'mr-auto rtl:mr-0 rtl:ml-auto': position === 'right' }, 'flex justify-between']}>
      <a class="flex items-center" href={`${lang !== defaultLang ? '/' + lang : '/'}`}>
        <Logo />
      </a>
      <div class="flex items-center md:hidden">
        <ToggleMenu />
      </div>
    </div>
    <nav
      class="items-center w-full md:w-auto hidden md:flex text-default overflow-y-auto overflow-x-hidden md:overflow-y-visible md:overflow-x-auto md:mx-5"
      aria-label="Main navigation"
    >
      <ul
        class="flex flex-col md:flex-row md:self-center w-full md:w-auto text-xl md:text-[0.9375rem] tracking-[0.01rem] font-medium"
      >
        {
          links.map(({ text, href, links }) => (
            <li class={links?.length ? 'dropdown' : ''}>
              {links?.length ? (
                <>
                  <button class="hover:text-link dark:hover:text-white px-4 py-3 flex items-center">
                    {t(`${text?.toLowerCase()}`)}
                    <Icon name="tabler:chevron-down" class="w-3.5 h-3.5 ml-0.5 rtl:ml-0 rtl:mr-0.5 hidden md:inline" />
                  </button>
                  <ul class="dropdown-menu md:backdrop-blur-md dark:md:bg-dark rounded md:absolute pl-4 md:pl-0 md:hidden font-medium md:bg-white/90 md:min-w-[200px] drop-shadow-xl">
                    {links.map(({ text: text2, href: href2 }) => (
                      <li>
                        <a
                          class:list={[
                            'first:rounded-t last:rounded-b md:hover:bg-gray-100 hover:text-link dark:hover:text-white dark:hover:bg-gray-700 py-2 px-5 block whitespace-no-wrap',
                            { 'aw-link-active': href2 === currentPath },
                          ]}
                          href={processRoute(href2, lang)}
                        >
                          {t(`${text2?.toLowerCase()}`)}
                        </a>
                      </li>
                    ))}
                  </ul>
                </>
              ) : (
                <a
                  class:list={[
                    'hover:text-link dark:hover:text-white px-4 py-3 flex items-centers',
                    { 'aw-link-active': href === currentPath },
                  ]}
                  href={processRoute(href, lang)}
                >
                  {t(`${text?.toLowerCase()}`)}
                </a>
              )}
            </li>
          ))
        }
      </ul>
    </nav>

    <div
      class:list={[
        { 'ml-auto rtl:ml-0 rtl:mr-auto': position === 'left' },
        'hidden md:self-center flex md:flex items-center md:mb-0 fixed w-full md:w-auto md:static justify-end left-0 rtl:left-auto rtl:right-0 bottom-0 p-3 md:p-0',
      ]}
    >
      <LanguagePicker />

      <div class="items-center flex justify-between w-full md:w-auto">
        <div class="flex">
          {showToggleTheme && <ToggleTheme iconClass="w-6 h-6 md:w-5 md:h-5 md:inline-block" />}
          {
            showRssFeed && (
              <a
                class="text-muted dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 inline-flex items-center"
                aria-label="RSS Feed"
                href={getAsset('/rss.xml')}
              >
                <Icon name="tabler:rss" class="w-5 h-5" />
              </a>
            )
          }
        </div>
        {
          actions?.length ? (
            <span class="ml-4 rtl:ml-0 rtl:mr-4">
              {actions.map(({ text, href, class: className }) => (
                <a
                  class:list={['btn ml-2 py-2.5 px-5.5 md:px-6 font-semibold shadow-none text-sm', className]}
                  href={href}
                >
                  <Fragment set:html={text} />
                </a>
              ))}
            </span>
          ) : (
            ''
          )
        }
      </div>
    </div>
  </div>
</header>
