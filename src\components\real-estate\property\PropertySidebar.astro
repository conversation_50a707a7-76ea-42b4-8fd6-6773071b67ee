---
import { Icon } from 'astro-icon/components';
import { getLangFromUrl, useTranslations, useTranslatedPath } from '~/i18n/utils';

// i18n setup
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
const translatePath = useTranslatedPath(lang);

interface Props {
  title: string;
  address: string;
  showQuickActions: boolean;
  contactInfo: {
    fullname: string;
    email: string;
    whatsapp: string;
    phone: string;
    category?: string;
    notes?: string;
  };
}

const { title, address, showQuickActions, contactInfo } = Astro.props;

// Get the current URL for sharing
const url = Astro.url.toString();

// Determine preferred contact methods
const hasWhatsApp = !!contactInfo.whatsapp;
const hasPhone = !!contactInfo.phone;
const hasEmail = !!contactInfo.email;

// Clean phone numbers for links (remove spaces, parentheses, etc.)
const cleanPhoneNumber = (number) => typeof number === 'string' ? number.replace(//s+/g, '').replace(/[()+-]/g, '') : '';
const whatsAppClean = cleanPhoneNumber(contactInfo.whatsapp);
const phoneClean = cleanPhoneNumber(contactInfo.phone);
---

<div class="lg:col-span-1">
  <div class="sticky top-8">
    
    <!-- Contact Form -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
      <div class="mb-4 p-2 text-center bg-gray-50 dark:bg-gray-900 rounded-lg">
        <span class="block text-2xl font-bold text-gray-900 dark:text-white">{t('contact_form')}</span>
        <span class="block text-sm text-gray-600 dark:text-gray-400 italic">{t('fill_out_fields')}</span>
      </div>

      {contactInfo.notes && (
        <div class="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-sm text-gray-700 dark:text-gray-300">
          <p>{contactInfo.notes}</p>
        </div>
      )}

      <form id="contact-form" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('first_name')}</label>
            <input
              id="first-name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('last_name')}</label>
            <input
              id="last-name"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('phone_number')}</label>
          <input
            id="phone"
            type="tel"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('email')}</label>
          <input
            id="email"
            type="email"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
          />
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">{t('message')}</label>
          <textarea
            id="message"
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            rows="4"></textarea>
        </div>

        <div class="mt-4">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('send_via')}</label>
          <div class="flex flex-wrap gap-4">
            {hasWhatsApp && (
              <label class="flex items-center space-x-3 cursor-pointer">
                <input type="radio" name="send-method" value="whatsapp" checked 
                  class="w-4 h-4 text-green-600 focus:ring-green-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700" />
                <div class="flex items-center">
                  <Icon name="tabler:brand-whatsapp" class="w-5 h-5 text-green-600 mr-2" />
                  <span class="text-gray-800 dark:text-white">{t('whatsapp') || 'WhatsApp'}</span>
                </div>
              </label>
            )}
            
            {hasEmail && (
              <label class="flex items-center space-x-3 cursor-pointer">
                <input type="radio" name="send-method" value="email" class={`w-4 h-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 dark:bg-gray-700 ${!hasWhatsApp ? 'checked' : ''}`} />
                <div class="flex items-center">
                  <Icon name="tabler:mail" class="w-5 h-5 text-blue-600 mr-2" />
                  <span class="text-gray-800 dark:text-white">{t('email') || 'Email'}</span>
                </div>
              </label>
            )}
          </div>
        </div>

        <div class="space-y-3">
          <button
            type="submit"
            disabled
            class="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-3 px-4 rounded-lg transition duration-150 opacity-50 cursor-not-allowed"
          >
            {t('submit')}
          </button>
          
        </div>
      </form>
    </div>

    <!-- Phone Number Display -->
    {hasPhone && (
      <div class="mt-4 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all hover:shadow-md">
        {contactInfo.fullname && (
          <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">
             <span class="font-medium">{contactInfo.fullname}</span>
            {contactInfo.category && (
              <span class="text-gray-500 dark:text-gray-400"> ({contactInfo.category})</span>
            )}
          </p>
        )}

        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <Icon name="tabler:phone" class="w-5 h-5 text-gray-600 dark:text-gray-400 mr-2" />
            <span class="text-sm text-gray-700 dark:text-gray-300">{t('contact_number') || 'Contact number'}</span>
          </div>
          <span class="font-semibold text-gray-900 dark:text-white">{contactInfo.phone}</span>
        </div>
      </div>
    )}

    <!-- Quick Actions -->
    {showQuickActions && (
      <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm mt-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{t('quick_actions')}</h3>
        <div class="space-y-3">
          {hasPhone && (
            <a
              href={`tel:+${phoneClean}`}
              class="flex items-center justify-center w-full bg-primary-600 hover:bg-primary-700 text-white py-3 px-4 rounded-lg transition-colors"
            >
              <Icon name="tabler:phone" class="w-5 h-5 mr-2" />
              <span>{t('call')}</span>
            </a>
          )}
          
          {hasWhatsApp && (
            <a
              href={`https://wa.me/${whatsAppClean}?text=${encodeURIComponent(`${t('interested_in')}: ${title} - ${address}`)}`}
              target="_blank"
              rel="noopener noreferrer"
              class="flex items-center justify-center w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg transition-colors"
            >
              <Icon name="tabler:brand-whatsapp" class="w-5 h-5 mr-2" />
              <span>{t('whatsapp')}</span>
            </a>
          )}

          <!-- Share Dropdown Menu -->
          <div class="relative" id="share-dropdown-container">
            <button 
              id="share-dropdown-button"
              type="button"
              class="flex items-center justify-center w-full border border-gray-300 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-800 dark:text-white py-3 px-4 rounded-lg transition-colors"
              aria-expanded="false"
            >
              <Icon name="tabler:share" class="w-5 h-5 mr-2" />
              <span>{t('share')}</span>
            </button>

            <!-- Dropdown Menu -->
            <div
              id="share-dropdown-menu"
              class="hidden absolute z-10 w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700"
              style="top: auto; bottom: calc(100% + 8px);"
            >
              <ul class="py-1">
                <!-- Native Share (Mobile) -->
                <li id="native-share-option" class="hidden">
                  <button
                    class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center text-gray-700 dark:text-gray-300"
                  >
                    <Icon name="tabler:share" class="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" />
                    <span>{t('share_native') || 'Share'}</span>
                  </button>
                </li>

                <!-- Copy Link -->
                <li>
                  <button
                    id="copy-link-button"
                    class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center text-gray-700 dark:text-gray-300"
                  >
                    <Icon name="tabler:link" class="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" />
                    <span>{t('copy_link') || 'Copy Link'}</span>
                  </button>
                </li>

                <!-- Facebook -->
                <li>
                  <a
                    href={`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center text-gray-700 dark:text-gray-300"
                  >
                    <Icon name="tabler:brand-facebook" class="w-5 h-5 mr-3 text-blue-600" />
                    <span>Facebook</span>
                  </a>
                </li>

                <!-- Twitter/X -->
                <li>
                  <a
                    href={`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center text-gray-700 dark:text-gray-300"
                  >
                    <Icon name="tabler:brand-x" class="w-5 h-5 mr-3 text-gray-800 dark:text-gray-200" />
                    <span>Twitter</span>
                  </a>
                </li>

                <!-- WhatsApp -->
                <li>
                  <a
                    href={`https://wa.me/?text=${encodeURIComponent(`${title} - ${url}`)}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center text-gray-700 dark:text-gray-300"
                  >
                    <Icon name="tabler:brand-whatsapp" class="w-5 h-5 mr-3 text-green-600" />
                    <span>WhatsApp</span>
                  </a>
                </li>

                <!-- Email -->
                <li>
                  <a
                    href={`mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(`${t('check_out_this_property') || 'Check out this property'}: ${url}`)}`}
                    class="w-full text-left px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center text-gray-700 dark:text-gray-300"
                  >
                    <Icon name="tabler:mail" class="w-5 h-5 mr-3 text-gray-500 dark:text-gray-400" />
                    <span>Email</span>
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
</div>

<!-- Toast Notification -->
<div
  id="toast-notification"
  class="fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg hidden flex items-center transition-opacity duration-300 opacity-0 z-50"
>
  <Icon name="tabler:check" class="w-5 h-5 mr-2 text-green-400" />
  <span id="toast-message"></span>
</div>

<script define:vars={{ whatsAppNumber: whatsAppClean, emailAddress: contactInfo.email, phoneNumber: phoneClean }}>
  document.addEventListener('DOMContentLoaded', () => {
    // DOM elements for form
    const form = document.getElementById('contact-form');
    const firstNameInput = document.getElementById('first-name');
    const lastNameInput = document.getElementById('last-name');
    const phoneInput = document.getElementById('phone');
    const emailInput = document.getElementById('email');
    const messageInput = document.getElementById('message');
    
    // DOM elements for sharing
    const shareButton = document.getElementById('share-dropdown-button');
    const shareMenu = document.getElementById('share-dropdown-menu');
    const nativeShareOption = document.getElementById('native-share-option');
    const copyLinkButton = document.getElementById('copy-link-button');
    const toastNotification = document.getElementById('toast-notification');
    const toastMessage = document.getElementById('toast-message');
    const shareDropdownContainer = document.getElementById('share-dropdown-container');
    
    // Get common values
    const url = window.location.href;
    const title = document.querySelector('h1')?.textContent || '';
    const address = ''; // This would be populated from props if needed

    // ----------------
    // TOAST FUNCTIONS
    // ----------------
    function showToast(message) {
      toastMessage.textContent = message;
      toastNotification.classList.remove('hidden');

      // Fade in
      setTimeout(() => {
        toastNotification.classList.remove('opacity-0');
        toastNotification.classList.add('opacity-100');
      }, 10);

      // Fade out after delay
      setTimeout(() => {
        toastNotification.classList.remove('opacity-100');
        toastNotification.classList.add('opacity-0');

        setTimeout(() => {
          toastNotification.classList.add('hidden');
        }, 300);
      }, 3000);
    }

    // ----------------
    // CONTACT FORM
    // ----------------
    if (form) {
      // Validate form fields
      function validateForm() {
        const firstName = firstNameInput?.value?.trim() || '';
        const lastName = lastNameInput?.value?.trim() || '';
        const phone = phoneInput?.value?.trim() || '';
        const email = emailInput?.value?.trim() || '';
        const message = messageInput?.value?.trim() || '';
        
        // Get the submit button
        const submitButton = form.querySelector('button[type="submit"]');
        
        // Validate that important fields are filled
        const isValid = (firstName !== '' && lastName !== '') && (phone !== '' || email !== '') && message !== '';
        
        // Enable/disable button based on validity
        if (submitButton) {
          submitButton.disabled = !isValid;
          
          // Update button style
          if (!isValid) {
            submitButton.classList.add('opacity-50', 'cursor-not-allowed');
            submitButton.classList.remove('hover:bg-red-700');
          } else {
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
            submitButton.classList.add('hover:bg-red-700');
          }
        }
        
        return isValid;
      }

      // Handle form inputs
      [firstNameInput, lastNameInput, phoneInput, emailInput, messageInput].forEach((input) => {
        if (input) {
          input.addEventListener('input', validateForm);
        }
      });

      // Handle form submission
      form.addEventListener('submit', (e) => {
        e.preventDefault();
        
        const firstName = firstNameInput?.value || '';
        const lastName = lastNameInput?.value || '';
        const phone = phoneInput?.value || '';
        const email = emailInput?.value || '';
        const message = messageInput?.value || '';
        const sendMethod = document.querySelector('input[name="send-method"]:checked')?.value || 'whatsapp';
        
        const fullName = `${firstName} ${lastName}`.trim();
        
        if (sendMethod === 'whatsapp' && whatsAppNumber) {
          // Prepare WhatsApp message
          let whatsappText = `${title}/n${url}/n`;
          whatsappText += `---/n`;

          if (message) {
            whatsappText += `/n${message}/n/n`;
            whatsappText += `---/n`;
          }

          if (fullName || address || phone || email) {
            whatsappText += `/n`;
            if (fullName) whatsappText += `${fullName}/n`;
            if (address) whatsappText += `${address}/n`;
            if (phone) whatsappText += `${phone}/n`;
            if (email) whatsappText += `${email}`;
          }          
          
          // Open WhatsApp with the message
          window.open(`https://wa.me/${whatsAppNumber}?text=${encodeURIComponent(whatsappText)}`, '_blank');
          showToast('Opening WhatsApp...');
        } else if (emailAddress) {
          // Prepare email content
          let emailSubject = `${title}`;
          if (address) emailSubject += ` - ${address}`;

          let emailBody = `${title}/n${url}/n/n`;
          if (fullName) emailBody += `Name: ${fullName}/n`;
          if (phone) emailBody += `Phone: ${phone}/n`;
          if (email) emailBody += `Email: ${email}/n/n`;
          if (message) emailBody += `Message:/n${message}/n/n`;
          
          // Open email client with the actual email address
          window.location.href = `mailto:${emailAddress}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;
          showToast('Opening email client...');
        }
      });
    }

    // ----------------
    // SHARE DROPDOWN
    // ----------------
    if (shareButton && shareMenu) {
      // Check if native sharing is available
      if (navigator.share) {
        nativeShareOption?.classList.remove('hidden');
      }

      // Show/hide share dropdown menu
      shareButton.addEventListener('click', (e) => {
        e.stopPropagation();
        const isExpanded = shareButton.getAttribute('aria-expanded') === 'true';

        if (isExpanded) {
          hideDropdown();
        } else {
          showDropdown();
        }
      });

      // Handle native sharing
      nativeShareOption?.addEventListener('click', async () => {
        try {
          await navigator.share({
            title: title,
            text: title,
            url: url,
          });
          showToast('Shared successfully!');
        } catch (err) {
          if (err.name !== 'AbortError') {
            showToast('Failed to share');
            console.error('Error sharing:', err);
          }
        }
        hideDropdown();
      });

      // Handle copy link
      copyLinkButton?.addEventListener('click', async () => {
        try {
          await navigator.clipboard.writeText(url);
          showToast('Link copied to clipboard!');
        } catch (err) {
          showToast('Failed to copy link');
          console.error('Error copying link:', err);
        }
        hideDropdown();
      });

      // Close dropdown when clicking outside
      document.addEventListener('click', (e) => {
        if (shareDropdownContainer && !shareDropdownContainer.contains(e.target)) {
          hideDropdown();
        }
      });

      // Close dropdown when pressing Escape
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          hideDropdown();
        }
      });

      // Helper functions for dropdown
      function showDropdown() {
        shareMenu.classList.remove('hidden');
        shareButton.setAttribute('aria-expanded', 'true');

        // Add animation
        setTimeout(() => {
          shareMenu.classList.add('animate-fadeIn');
        }, 10);
      }

      function hideDropdown() {
        shareMenu.classList.remove('animate-fadeIn');
        shareButton.setAttribute('aria-expanded', 'false');

        // Delay hiding to allow animation to finish
        setTimeout(() => {
          shareMenu.classList.add('hidden');
        }, 200);
      }
    }
  });
</script>

<style>
  /* Animations */
  .animate-fadeIn {
    animation: fadeIn 0.2s ease-in-out forwards;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>